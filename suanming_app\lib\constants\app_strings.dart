class AppStrings {
  // 应用信息
  static const String appName = '八字命理';
  static const String appDescription = '专业的命理测算系统';
  static const String version = '1.0.0';
  
  // 登录页面
  static const String loginTitle = '欢迎回来';
  static const String loginDescription = '请登录您的账户继续使用';
  static const String username = '用户名';
  static const String password = '密码';
  static const String login = '登录';
  static const String register = '注册';
  static const String forgotPassword = '忘记密码？';
  static const String agreePrivacy = '我已阅读并同意';
  static const String privacyPolicy = '《隐私政策》';
  
  // 注册页面
  static const String registerTitle = '创建账户';
  static const String registerDescription = '请填写以下信息完成注册';
  static const String phone = '手机号';
  static const String verificationCode = '验证码';
  static const String getCode = '获取验证码';
  static const String confirmPassword = '确认密码';
  static const String inviteCode = '邀请码（可选）';
  static const String alreadyHaveAccount = '已有账户？';
  
  // 找回密码页面
  static const String resetPasswordTitle = '找回密码';
  static const String resetPasswordDescription = '请输入您的手机号码';
  static const String newPassword = '新密码';
  static const String confirmNewPassword = '确认新密码';
  static const String resetPassword = '重置密码';
  
  // 首页
  static const String home = '首页';
  static const String aiChat = 'AI对话';
  static const String selectAgent = '选择智能体';
  static const String selectBazi = '选择八字';
  static const String inputMessage = '请输入您的问题...';
  static const String send = '发送';
  static const String newChat = '新建对话';
  static const String chatHistory = '对话历史';
  
  // 排盘页面
  static const String paipan = '排盘';
  static const String baziPaipan = '八字排盘';
  static const String name = '姓名';
  static const String gender = '性别';
  static const String male = '男';
  static const String female = '女';
  static const String birthYear = '出生年';
  static const String birthMonth = '出生月';
  static const String birthDay = '出生日';
  static const String birthHour = '出生时';
  static const String birthPlace = '出生地';
  static const String province = '省份';
  static const String city = '城市';
  static const String county = '区县';
  static const String startPaipan = '开始排盘';
  static const String paipanResult = '排盘结果';
  static const String paipanHistory = '排盘历史';
  static const String otherCategories = '其他类目';
  static const String inDevelopment = '正在开发中...';
  
  // 个人中心
  static const String profile = '个人中心';
  static const String userInfo = '用户信息';
  static const String nickname = '昵称';
  static const String account = '账号';
  static const String computing = '算力';
  static const String recharge = '充值';
  static const String computingRecord = '算力记录';
  static const String clearLocalData = '清除本地数据';
  static const String aboutUs = '关于我们';
  static const String logout = '退出登录';
  
  // 通用
  static const String confirm = '确认';
  static const String cancel = '取消';
  static const String save = '保存';
  static const String edit = '编辑';
  static const String delete = '删除';
  static const String loading = '加载中...';
  static const String noData = '暂无数据';
  static const String networkError = '网络错误';
  static const String retry = '重试';
  
  // 错误信息
  static const String usernameRequired = '请输入用户名';
  static const String passwordRequired = '请输入密码';
  static const String phoneRequired = '请输入手机号';
  static const String codeRequired = '请输入验证码';
  static const String passwordMismatch = '两次输入的密码不一致';
  static const String invalidPhone = '请输入正确的手机号';
  static const String loginFailed = '登录失败';
  static const String registerFailed = '注册失败';
}
