<component name="libraryTable">
  <library name="KotlinJavaRuntime">
    <CLASSES>
      <root url="jar://$KOTLIN_BUNDLED$/lib/kotlin-stdlib.jar!/" />
      <root url="jar://$KOTLIN_BUNDLED$/lib/kotlin-reflect.jar!/" />
      <root url="jar://$KOTLIN_BUNDLED$/lib/kotlin-test.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$KOTLIN_BUNDLED$/lib/kotlin-stdlib-sources.jar!/" />
      <root url="jar://$KOTLIN_BUNDLED$/lib/kotlin-reflect-sources.jar!/" />
      <root url="jar://$KOTLIN_BUNDLED$/lib/kotlin-test-sources.jar!/" />
    </SOURCES>
  </library>
</component>
