^F:\SUANMING\0730-CHATUI\SUANMING_APP\BUILD\WINDOWS\X64\CMAKEFILES\9E6155428291B8E9A0CF388389F95F66\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SF:/suanming/0730-chatui/suanming_app/windows -BF:/suanming/0730-chatui/suanming_app/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file F:/suanming/0730-chatui/suanming_app/build/windows/x64/suanming_app.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
