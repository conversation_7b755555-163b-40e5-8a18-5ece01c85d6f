^F:\SUANMING\0730-CHATUI\SUANMING_APP\BUILD\WINDOWS\X64\CMAKEFILES\A4C64E8A6443C7EE7AC04CC13B6A7689\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=D:\flutter\flutter PROJECT_DIR=F:\suanming\0730-chatui\suanming_app FLUTTER_ROOT=D:\flutter\flutter FLUTTER_EPHEMERAL_DIR=F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral PROJECT_DIR=F:\suanming\0730-chatui\suanming_app FLUTTER_TARGET=F:\suanming\0730-chatui\suanming_app\lib\main.dart DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=F:\suanming\0730-chatui\suanming_app\.dart_tool\package_config.json D:/flutter/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^F:\SUANMING\0730-CHATUI\SUANMING_APP\BUILD\WINDOWS\X64\CMAKEFILES\F699025FFF4D6711C0C7B89E1884E724\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^F:\SUANMING\0730-CHATUI\SUANMING_APP\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SF:/suanming/0730-chatui/suanming_app/windows -BF:/suanming/0730-chatui/suanming_app/build/windows/x64 --check-stamp-file F:/suanming/0730-chatui/suanming_app/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
