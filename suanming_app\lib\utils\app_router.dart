import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../pages/login_page.dart';
import '../pages/register_page.dart';
import '../pages/forgot_password_page.dart';
import '../pages/main_page.dart';
import '../pages/privacy_policy_page.dart';
import '../pages/recharge_page.dart';
import '../pages/computing_record_page.dart';

class AppRouter {
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String main = '/main';
  static const String privacyPolicy = '/privacy-policy';
  static const String recharge = '/recharge';
  static const String computingRecord = '/computing-record';

  static final GoRouter router = GoRouter(
    initialLocation: login,
    routes: [
      GoRoute(
        path: login,
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),
      GoRoute(
        path: register,
        name: 'register',
        builder: (context, state) => const RegisterPage(),
      ),
      GoRoute(
        path: forgotPassword,
        name: 'forgot-password',
        builder: (context, state) => const ForgotPasswordPage(),
      ),
      GoRoute(
        path: main,
        name: 'main',
        builder: (context, state) => const MainPage(),
      ),
      GoRoute(
        path: privacyPolicy,
        name: 'privacy-policy',
        builder: (context, state) => const PrivacyPolicyPage(),
      ),
      GoRoute(
        path: recharge,
        name: 'recharge',
        builder: (context, state) => const RechargePage(),
      ),
      GoRoute(
        path: computingRecord,
        name: 'computing-record',
        builder: (context, state) => const ComputingRecordPage(),
      ),
    ],
  );
}
