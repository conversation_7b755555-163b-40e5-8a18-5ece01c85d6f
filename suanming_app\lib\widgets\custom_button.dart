import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../constants/app_colors.dart';
import '../constants/app_styles.dart';

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isOutlined;
  final double? width;
  final double? height;
  final Color? backgroundColor;
  final Color? textColor;
  final IconData? icon;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.isOutlined = false,
    this.width,
    this.height,
    this.backgroundColor,
    this.textColor,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final buttonHeight = height ?? 48.h;
    
    if (isOutlined) {
      return SizedBox(
        width: width,
        height: buttonHeight,
        child: OutlinedButton.icon(
          onPressed: isLoading ? null : onPressed,
          style: AppStyles.secondaryButtonStyle.copyWith(
            minimumSize: WidgetStateProperty.all(Size(0, buttonHeight)),
          ),
          icon: isLoading
              ? SizedBox(
                  width: 16.w,
                  height: 16.w,
                  child: const CircularProgressIndicator(strokeWidth: 2),
                )
              : icon != null
                  ? Icon(icon, size: 18.w)
                  : const SizedBox.shrink(),
          label: Text(
            text,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: textColor ?? AppColors.primary,
            ),
          ),
        ),
      );
    }

    return SizedBox(
      width: width,
      height: buttonHeight,
      child: ElevatedButton.icon(
        onPressed: isLoading ? null : onPressed,
        style: AppStyles.primaryButtonStyle.copyWith(
          backgroundColor: WidgetStateProperty.all(
            backgroundColor ?? AppColors.primary,
          ),
          minimumSize: WidgetStateProperty.all(Size(0, buttonHeight)),
        ),
        icon: isLoading
            ? SizedBox(
                width: 16.w,
                height: 16.w,
                child: const CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : icon != null
                ? Icon(icon, size: 18.w, color: Colors.white)
                : const SizedBox.shrink(),
        label: Text(
          text,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
            color: textColor ?? Colors.white,
          ),
        ),
      ),
    );
  }
}
