﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{6C630D30-9F26-3478-89BE-534B0A6B14E3}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>flutter_assemble</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="F:\suanming\0730-chatui\suanming_app\build\windows\x64\CMakeFiles\a4c64e8a6443c7ee7ac04cc13b6a7689\flutter_windows.dll.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Generating F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/flutter_windows.dll, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/flutter_export.h, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/flutter_windows.h, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/flutter_messenger.h, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/flutter_plugin_registrar.h, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/flutter_texture_registrar.h, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/cpp_client_wrapper/core_implementations.cc, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/cpp_client_wrapper/standard_codec.cc, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/cpp_client_wrapper/plugin_registrar.cc, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/cpp_client_wrapper/flutter_engine.cc, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/cpp_client_wrapper/flutter_view_controller.cc, _phony_</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=D:\flutter\flutter PROJECT_DIR=F:\suanming\0730-chatui\suanming_app FLUTTER_ROOT=D:\flutter\flutter FLUTTER_EPHEMERAL_DIR=F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral PROJECT_DIR=F:\suanming\0730-chatui\suanming_app FLUTTER_TARGET=F:\suanming\0730-chatui\suanming_app\lib\main.dart DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=F:\suanming\0730-chatui\suanming_app\.dart_tool\package_config.json D:/flutter/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_windows.dll;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_export.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_windows.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_messenger.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_plugin_registrar.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_texture_registrar.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;F:\suanming\0730-chatui\suanming_app\build\windows\x64\flutter\_phony_</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Generating F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/flutter_windows.dll, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/flutter_export.h, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/flutter_windows.h, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/flutter_messenger.h, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/flutter_plugin_registrar.h, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/flutter_texture_registrar.h, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/cpp_client_wrapper/core_implementations.cc, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/cpp_client_wrapper/standard_codec.cc, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/cpp_client_wrapper/plugin_registrar.cc, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/cpp_client_wrapper/flutter_engine.cc, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/cpp_client_wrapper/flutter_view_controller.cc, _phony_</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=D:\flutter\flutter PROJECT_DIR=F:\suanming\0730-chatui\suanming_app FLUTTER_ROOT=D:\flutter\flutter FLUTTER_EPHEMERAL_DIR=F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral PROJECT_DIR=F:\suanming\0730-chatui\suanming_app FLUTTER_TARGET=F:\suanming\0730-chatui\suanming_app\lib\main.dart DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=F:\suanming\0730-chatui\suanming_app\.dart_tool\package_config.json D:/flutter/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Profile
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_windows.dll;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_export.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_windows.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_messenger.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_plugin_registrar.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_texture_registrar.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;F:\suanming\0730-chatui\suanming_app\build\windows\x64\flutter\_phony_</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Generating F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/flutter_windows.dll, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/flutter_export.h, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/flutter_windows.h, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/flutter_messenger.h, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/flutter_plugin_registrar.h, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/flutter_texture_registrar.h, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/cpp_client_wrapper/core_implementations.cc, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/cpp_client_wrapper/standard_codec.cc, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/cpp_client_wrapper/plugin_registrar.cc, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/cpp_client_wrapper/flutter_engine.cc, F:/suanming/0730-chatui/suanming_app/windows/flutter/ephemeral/cpp_client_wrapper/flutter_view_controller.cc, _phony_</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=D:\flutter\flutter PROJECT_DIR=F:\suanming\0730-chatui\suanming_app FLUTTER_ROOT=D:\flutter\flutter FLUTTER_EPHEMERAL_DIR=F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral PROJECT_DIR=F:\suanming\0730-chatui\suanming_app FLUTTER_TARGET=F:\suanming\0730-chatui\suanming_app\lib\main.dart DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=F:\suanming\0730-chatui\suanming_app\.dart_tool\package_config.json D:/flutter/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_windows.dll;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_export.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_windows.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_messenger.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_plugin_registrar.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_texture_registrar.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;F:\suanming\0730-chatui\suanming_app\build\windows\x64\flutter\_phony_</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\suanming\0730-chatui\suanming_app\build\windows\x64\CMakeFiles\f699025fff4d6711c0c7b89e1884e724\flutter_assemble.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_windows.dll;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_export.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_windows.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_messenger.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_plugin_registrar.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_texture_registrar.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\suanming\0730-chatui\suanming_app\build\windows\x64\flutter\CMakeFiles\flutter_assemble</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_windows.dll;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_export.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_windows.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_messenger.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_plugin_registrar.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_texture_registrar.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">F:\suanming\0730-chatui\suanming_app\build\windows\x64\flutter\CMakeFiles\flutter_assemble</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_windows.dll;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_export.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_windows.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_messenger.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_plugin_registrar.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\flutter_texture_registrar.h;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\suanming\0730-chatui\suanming_app\build\windows\x64\flutter\CMakeFiles\flutter_assemble</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\suanming\0730-chatui\suanming_app\windows\flutter\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule F:/suanming/0730-chatui/suanming_app/windows/flutter/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SF:/suanming/0730-chatui/suanming_app/windows -BF:/suanming/0730-chatui/suanming_app/build/windows/x64 --check-stamp-file F:/suanming/0730-chatui/suanming_app/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\generated_config.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\suanming\0730-chatui\suanming_app\build\windows\x64\flutter\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Building Custom Rule F:/suanming/0730-chatui/suanming_app/windows/flutter/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SF:/suanming/0730-chatui/suanming_app/windows -BF:/suanming/0730-chatui/suanming_app/build/windows/x64 --check-stamp-file F:/suanming/0730-chatui/suanming_app/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\generated_config.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">F:\suanming\0730-chatui\suanming_app\build\windows\x64\flutter\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule F:/suanming/0730-chatui/suanming_app/windows/flutter/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SF:/suanming/0730-chatui/suanming_app/windows -BF:/suanming/0730-chatui/suanming_app/build/windows/x64 --check-stamp-file F:/suanming/0730-chatui/suanming_app/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\suanming\0730-chatui\suanming_app\windows\flutter\ephemeral\generated_config.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\suanming\0730-chatui\suanming_app\build\windows\x64\flutter\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include="F:\suanming\0730-chatui\suanming_app\build\windows\x64\flutter\CMakeFiles\flutter_assemble">
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="F:\suanming\0730-chatui\suanming_app\build\windows\x64\ZERO_CHECK.vcxproj">
      <Project>{6950E804-3D17-35D1-9087-624327C42F9F}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>