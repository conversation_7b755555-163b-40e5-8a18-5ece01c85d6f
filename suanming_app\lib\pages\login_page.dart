import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import '../constants/app_colors.dart';
import '../constants/app_strings.dart';
import '../constants/app_styles.dart';
import '../utils/app_router.dart';
import '../widgets/custom_button.dart';
import '../widgets/custom_text_field.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isPasswordVisible = false;
  bool _agreePrivacy = false;
  bool _isLoading = false;

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _handleLogin() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!_agreePrivacy) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请先同意隐私政策')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // 模拟登录请求
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isLoading = false;
    });

    // 模拟登录成功，跳转到主页
    if (mounted) {
      context.go(AppRouter.main);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 32.w),
              child: Container(
                padding: EdgeInsets.all(32.w),
                decoration: AppStyles.cardDecoration,
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Logo
                      Container(
                        width: 80.w,
                        height: 80.w,
                        decoration: BoxDecoration(
                          color: AppColors.primary,
                          borderRadius: BorderRadius.circular(20.r),
                        ),
                        child: Icon(
                          Icons.auto_awesome,
                          size: 40.w,
                          color: Colors.white,
                        ),
                      ),
                      SizedBox(height: 24.h),

                      // 标题
                      Text(
                        AppStrings.loginTitle,
                        style: AppStyles.titleLarge.copyWith(fontSize: 28.sp),
                      ),
                      SizedBox(height: 8.h),

                      // 描述
                      Text(
                        AppStrings.loginDescription,
                        style: AppStyles.bodyMedium.copyWith(fontSize: 16.sp),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 32.h),

                      // 用户名输入框
                      CustomTextField(
                        controller: _usernameController,
                        labelText: AppStrings.username,
                        prefixIcon: Icons.person_outline,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return AppStrings.usernameRequired;
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 16.h),

                      // 密码输入框
                      CustomTextField(
                        controller: _passwordController,
                        labelText: AppStrings.password,
                        prefixIcon: Icons.lock_outline,
                        obscureText: !_isPasswordVisible,
                        suffixIcon: IconButton(
                          icon: Icon(
                            _isPasswordVisible ? Icons.visibility : Icons.visibility_off,
                          ),
                          onPressed: () {
                            setState(() {
                              _isPasswordVisible = !_isPasswordVisible;
                            });
                          },
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return AppStrings.passwordRequired;
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 16.h),

                      // 隐私协议
                      Row(
                        children: [
                          Checkbox(
                            value: _agreePrivacy,
                            onChanged: (value) {
                              setState(() {
                                _agreePrivacy = value ?? false;
                              });
                            },
                          ),
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  _agreePrivacy = !_agreePrivacy;
                                });
                              },
                              child: RichText(
                                text: TextSpan(
                                  style: AppStyles.bodySmall.copyWith(fontSize: 14.sp),
                                  children: [
                                    const TextSpan(text: AppStrings.agreePrivacy),
                                    TextSpan(
                                      text: AppStrings.privacyPolicy,
                                      style: TextStyle(
                                        color: AppColors.primary,
                                        decoration: TextDecoration.underline,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 24.h),

                      // 登录按钮
                      CustomButton(
                        text: AppStrings.login,
                        onPressed: _isLoading ? null : _handleLogin,
                        isLoading: _isLoading,
                        width: double.infinity,
                      ),
                      SizedBox(height: 16.h),

                      // 注册按钮
                      CustomButton(
                        text: AppStrings.register,
                        onPressed: () => context.go(AppRouter.register),
                        isOutlined: true,
                        width: double.infinity,
                      ),
                      SizedBox(height: 16.h),

                      // 忘记密码
                      TextButton(
                        onPressed: () => context.go(AppRouter.forgotPassword),
                        child: Text(
                          AppStrings.forgotPassword,
                          style: TextStyle(
                            color: AppColors.primary,
                            fontSize: 14.sp,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
