import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import '../constants/app_colors.dart';
import '../constants/app_strings.dart';
import '../constants/app_styles.dart';
import '../utils/app_router.dart';
import '../widgets/custom_button.dart';
import '../widgets/custom_text_field.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isPasswordVisible = false;
  bool _agreePrivacy = false;
  bool _isLoading = false;

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _handleLogin() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!_agreePrivacy) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请先同意隐私政策')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // 模拟登录请求
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isLoading = false;
    });

    // 模拟登录成功，跳转到主页
    if (mounted) {
      context.go(AppRouter.main);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: Stack(
          children: [
            // 装饰性的几何形状
            _buildDecorations(),

            // 主要内容
            SafeArea(
              child: Center(
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(horizontal: 40.w),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // 顶部标题
                      Padding(
                        padding: EdgeInsets.only(bottom: 60.h),
                        child: Column(
                          children: [
                            Text(
                              'Journey Cartoon',
                              style: TextStyle(
                                fontSize: 24.sp,
                                fontWeight: FontWeight.bold,
                                color: AppColors.decorativeOrange,
                              ),
                            ),
                            SizedBox(height: 4.h),
                            Text(
                              AppStrings.appDescription,
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // 登录卡片和卡通角色的容器
                      Stack(
                        children: [
                          // 登录卡片
                          Container(
                            width: 280.w,
                            padding: EdgeInsets.all(24.w),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(20.r),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 20,
                                  offset: const Offset(0, 10),
                                ),
                              ],
                            ),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            children: [
                              // Logo
                              Container(
                                width: 80.w,
                                height: 80.w,
                                decoration: BoxDecoration(
                                  color: AppColors.primary,
                                  borderRadius: BorderRadius.circular(40.r),
                                ),
                                child: Center(
                                  child: Text(
                                    'C',
                                    style: TextStyle(
                                      fontSize: 36.sp,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(height: 16.h),

                              // 应用名称
                              Text(
                                AppStrings.appName,
                                style: TextStyle(
                                  fontSize: 24.sp,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                              SizedBox(height: 32.h),

                              // 用户名输入框
                              Container(
                                decoration: BoxDecoration(
                                  color: AppColors.background,
                                  borderRadius: BorderRadius.circular(12.r),
                                ),
                                child: TextFormField(
                                  controller: _usernameController,
                                  style: TextStyle(fontSize: 16.sp),
                                  decoration: InputDecoration(
                                    hintText: '用户名',
                                    hintStyle: TextStyle(
                                      color: AppColors.textHint,
                                      fontSize: 16.sp,
                                    ),
                                    prefixIcon: Icon(
                                      Icons.person_outline,
                                      color: AppColors.textHint,
                                      size: 20.w,
                                    ),
                                    border: InputBorder.none,
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: 16.w,
                                      vertical: 16.h,
                                    ),
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return AppStrings.usernameRequired;
                                    }
                                    return null;
                                  },
                                ),
                              ),
                              SizedBox(height: 16.h),

                              // 密码输入框
                              Container(
                                decoration: BoxDecoration(
                                  color: AppColors.background,
                                  borderRadius: BorderRadius.circular(12.r),
                                ),
                                child: TextFormField(
                                  controller: _passwordController,
                                  obscureText: !_isPasswordVisible,
                                  style: TextStyle(fontSize: 16.sp),
                                  decoration: InputDecoration(
                                    hintText: '密码',
                                    hintStyle: TextStyle(
                                      color: AppColors.textHint,
                                      fontSize: 16.sp,
                                    ),
                                    prefixIcon: Icon(
                                      Icons.lock_outline,
                                      color: AppColors.textHint,
                                      size: 20.w,
                                    ),
                                    suffixIcon: IconButton(
                                      icon: Icon(
                                        _isPasswordVisible ? Icons.visibility : Icons.visibility_off,
                                        color: AppColors.textHint,
                                        size: 20.w,
                                      ),
                                      onPressed: () {
                                        setState(() {
                                          _isPasswordVisible = !_isPasswordVisible;
                                        });
                                      },
                                    ),
                                    border: InputBorder.none,
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: 16.w,
                                      vertical: 16.h,
                                    ),
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return AppStrings.passwordRequired;
                                    }
                                    return null;
                                  },
                                ),
                              ),
                              SizedBox(height: 24.h),

                              // 登录按钮
                              Container(
                                width: double.infinity,
                                height: 48.h,
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [AppColors.primary, AppColors.primaryLight],
                                    begin: Alignment.centerLeft,
                                    end: Alignment.centerRight,
                                  ),
                                  borderRadius: BorderRadius.circular(24.r),
                                ),
                                child: ElevatedButton(
                                  onPressed: _isLoading ? null : _handleLogin,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.transparent,
                                    shadowColor: Colors.transparent,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(24.r),
                                    ),
                                  ),
                                  child: _isLoading
                                      ? SizedBox(
                                          width: 20.w,
                                          height: 20.w,
                                          child: const CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                          ),
                                        )
                                      : Text(
                                          '登录',
                                          style: TextStyle(
                                            fontSize: 16.sp,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.white,
                                          ),
                                        ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      // 卡通角色
                      Positioned(
                        right: -20.w,
                        top: 100.h,
                        child: _buildCartoonCharacter(),
                      ),

                      // 底部选项
                      Positioned(
                        bottom: 40.h,
                        right: 40.w,
                        child: _buildBottomOptions(),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 装饰性几何形状
  Widget _buildDecorations() {
    return Stack(
      children: [
        // 左上角装饰
        Positioned(
          top: -50.h,
          left: -50.w,
          child: Container(
            width: 200.w,
            height: 200.w,
            decoration: BoxDecoration(
              color: AppColors.decorativeOrange.withOpacity(0.3),
              borderRadius: BorderRadius.circular(100.r),
            ),
          ),
        ),
        // 右下角装饰
        Positioned(
          bottom: -100.h,
          right: -100.w,
          child: Container(
            width: 300.w,
            height: 300.w,
            decoration: BoxDecoration(
              color: AppColors.decorativeOrangeLight.withOpacity(0.2),
              borderRadius: BorderRadius.circular(150.r),
            ),
          ),
        ),
        // 小圆点装饰
        Positioned(
          top: 200.h,
          left: 50.w,
          child: Container(
            width: 20.w,
            height: 20.w,
            decoration: BoxDecoration(
              color: AppColors.decorativeOrange,
              borderRadius: BorderRadius.circular(10.r),
            ),
          ),
        ),
        Positioned(
          bottom: 300.h,
          right: 80.w,
          child: Container(
            width: 15.w,
            height: 15.w,
            decoration: BoxDecoration(
              color: AppColors.decorativeOrangeLight,
              borderRadius: BorderRadius.circular(7.5.r),
            ),
          ),
        ),
      ],
    );
  }

  // 卡通角色
  Widget _buildCartoonCharacter() {
    return Container(
      width: 120.w,
      height: 120.w,
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(60.r),
      ),
      child: Icon(
        Icons.person,
        size: 60.w,
        color: AppColors.primary,
      ),
    );
  }

  // 底部选项
  Widget _buildBottomOptions() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12.w,
          height: 12.w,
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.circular(6.r),
          ),
        ),
        SizedBox(width: 8.w),
        Container(
          width: 12.w,
          height: 12.w,
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(6.r),
          ),
        ),
        SizedBox(width: 16.w),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Dark mode',
              style: TextStyle(
                fontSize: 10.sp,
                color: AppColors.textSecondary,
              ),
            ),
            Text(
              'Customer Service',
              style: TextStyle(
                fontSize: 10.sp,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
