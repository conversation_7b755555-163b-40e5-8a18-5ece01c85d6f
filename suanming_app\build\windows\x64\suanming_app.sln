﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{C85B6D6E-E192-3DA9-AC24-5EDEE9786733}"
	ProjectSection(ProjectDependencies) = postProject
		{6950E804-3D17-35D1-9087-624327C42F9F} = {6950E804-3D17-35D1-9087-624327C42F9F}
		{8C85A0C7-AFD2-3EE8-BB53-50368F3A3EDC} = {8C85A0C7-AFD2-3EE8-BB53-50368F3A3EDC}
		{CD6295E0-A661-30C0-94BB-274DFB188FA4} = {CD6295E0-A661-30C0-94BB-274DFB188FA4}
		{800676E5-ACA7-3EA5-ADC5-2B54FD731086} = {800676E5-ACA7-3EA5-ADC5-2B54FD731086}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{C3983CD5-673A-3DAE-8EFA-F1131CF8637B}"
	ProjectSection(ProjectDependencies) = postProject
		{C85B6D6E-E192-3DA9-AC24-5EDEE9786733} = {C85B6D6E-E192-3DA9-AC24-5EDEE9786733}
		{6950E804-3D17-35D1-9087-624327C42F9F} = {6950E804-3D17-35D1-9087-624327C42F9F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{6950E804-3D17-35D1-9087-624327C42F9F}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "flutter\flutter_assemble.vcxproj", "{6C630D30-9F26-3478-89BE-534B0A6B14E3}"
	ProjectSection(ProjectDependencies) = postProject
		{6950E804-3D17-35D1-9087-624327C42F9F} = {6950E804-3D17-35D1-9087-624327C42F9F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_app", "flutter\flutter_wrapper_app.vcxproj", "{8C85A0C7-AFD2-3EE8-BB53-50368F3A3EDC}"
	ProjectSection(ProjectDependencies) = postProject
		{6950E804-3D17-35D1-9087-624327C42F9F} = {6950E804-3D17-35D1-9087-624327C42F9F}
		{6C630D30-9F26-3478-89BE-534B0A6B14E3} = {6C630D30-9F26-3478-89BE-534B0A6B14E3}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "flutter\flutter_wrapper_plugin.vcxproj", "{CD6295E0-A661-30C0-94BB-274DFB188FA4}"
	ProjectSection(ProjectDependencies) = postProject
		{6950E804-3D17-35D1-9087-624327C42F9F} = {6950E804-3D17-35D1-9087-624327C42F9F}
		{6C630D30-9F26-3478-89BE-534B0A6B14E3} = {6C630D30-9F26-3478-89BE-534B0A6B14E3}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "suanming_app", "runner\suanming_app.vcxproj", "{800676E5-ACA7-3EA5-ADC5-2B54FD731086}"
	ProjectSection(ProjectDependencies) = postProject
		{6950E804-3D17-35D1-9087-624327C42F9F} = {6950E804-3D17-35D1-9087-624327C42F9F}
		{6C630D30-9F26-3478-89BE-534B0A6B14E3} = {6C630D30-9F26-3478-89BE-534B0A6B14E3}
		{8C85A0C7-AFD2-3EE8-BB53-50368F3A3EDC} = {8C85A0C7-AFD2-3EE8-BB53-50368F3A3EDC}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C85B6D6E-E192-3DA9-AC24-5EDEE9786733}.Debug|x64.ActiveCfg = Debug|x64
		{C85B6D6E-E192-3DA9-AC24-5EDEE9786733}.Debug|x64.Build.0 = Debug|x64
		{C85B6D6E-E192-3DA9-AC24-5EDEE9786733}.Profile|x64.ActiveCfg = Profile|x64
		{C85B6D6E-E192-3DA9-AC24-5EDEE9786733}.Profile|x64.Build.0 = Profile|x64
		{C85B6D6E-E192-3DA9-AC24-5EDEE9786733}.Release|x64.ActiveCfg = Release|x64
		{C85B6D6E-E192-3DA9-AC24-5EDEE9786733}.Release|x64.Build.0 = Release|x64
		{C3983CD5-673A-3DAE-8EFA-F1131CF8637B}.Debug|x64.ActiveCfg = Debug|x64
		{C3983CD5-673A-3DAE-8EFA-F1131CF8637B}.Debug|x64.Build.0 = Debug|x64
		{C3983CD5-673A-3DAE-8EFA-F1131CF8637B}.Profile|x64.ActiveCfg = Profile|x64
		{C3983CD5-673A-3DAE-8EFA-F1131CF8637B}.Profile|x64.Build.0 = Profile|x64
		{C3983CD5-673A-3DAE-8EFA-F1131CF8637B}.Release|x64.ActiveCfg = Release|x64
		{C3983CD5-673A-3DAE-8EFA-F1131CF8637B}.Release|x64.Build.0 = Release|x64
		{6950E804-3D17-35D1-9087-624327C42F9F}.Debug|x64.ActiveCfg = Debug|x64
		{6950E804-3D17-35D1-9087-624327C42F9F}.Debug|x64.Build.0 = Debug|x64
		{6950E804-3D17-35D1-9087-624327C42F9F}.Profile|x64.ActiveCfg = Profile|x64
		{6950E804-3D17-35D1-9087-624327C42F9F}.Profile|x64.Build.0 = Profile|x64
		{6950E804-3D17-35D1-9087-624327C42F9F}.Release|x64.ActiveCfg = Release|x64
		{6950E804-3D17-35D1-9087-624327C42F9F}.Release|x64.Build.0 = Release|x64
		{6C630D30-9F26-3478-89BE-534B0A6B14E3}.Debug|x64.ActiveCfg = Debug|x64
		{6C630D30-9F26-3478-89BE-534B0A6B14E3}.Debug|x64.Build.0 = Debug|x64
		{6C630D30-9F26-3478-89BE-534B0A6B14E3}.Profile|x64.ActiveCfg = Profile|x64
		{6C630D30-9F26-3478-89BE-534B0A6B14E3}.Profile|x64.Build.0 = Profile|x64
		{6C630D30-9F26-3478-89BE-534B0A6B14E3}.Release|x64.ActiveCfg = Release|x64
		{6C630D30-9F26-3478-89BE-534B0A6B14E3}.Release|x64.Build.0 = Release|x64
		{8C85A0C7-AFD2-3EE8-BB53-50368F3A3EDC}.Debug|x64.ActiveCfg = Debug|x64
		{8C85A0C7-AFD2-3EE8-BB53-50368F3A3EDC}.Debug|x64.Build.0 = Debug|x64
		{8C85A0C7-AFD2-3EE8-BB53-50368F3A3EDC}.Profile|x64.ActiveCfg = Profile|x64
		{8C85A0C7-AFD2-3EE8-BB53-50368F3A3EDC}.Profile|x64.Build.0 = Profile|x64
		{8C85A0C7-AFD2-3EE8-BB53-50368F3A3EDC}.Release|x64.ActiveCfg = Release|x64
		{8C85A0C7-AFD2-3EE8-BB53-50368F3A3EDC}.Release|x64.Build.0 = Release|x64
		{CD6295E0-A661-30C0-94BB-274DFB188FA4}.Debug|x64.ActiveCfg = Debug|x64
		{CD6295E0-A661-30C0-94BB-274DFB188FA4}.Debug|x64.Build.0 = Debug|x64
		{CD6295E0-A661-30C0-94BB-274DFB188FA4}.Profile|x64.ActiveCfg = Profile|x64
		{CD6295E0-A661-30C0-94BB-274DFB188FA4}.Profile|x64.Build.0 = Profile|x64
		{CD6295E0-A661-30C0-94BB-274DFB188FA4}.Release|x64.ActiveCfg = Release|x64
		{CD6295E0-A661-30C0-94BB-274DFB188FA4}.Release|x64.Build.0 = Release|x64
		{800676E5-ACA7-3EA5-ADC5-2B54FD731086}.Debug|x64.ActiveCfg = Debug|x64
		{800676E5-ACA7-3EA5-ADC5-2B54FD731086}.Debug|x64.Build.0 = Debug|x64
		{800676E5-ACA7-3EA5-ADC5-2B54FD731086}.Profile|x64.ActiveCfg = Profile|x64
		{800676E5-ACA7-3EA5-ADC5-2B54FD731086}.Profile|x64.Build.0 = Profile|x64
		{800676E5-ACA7-3EA5-ADC5-2B54FD731086}.Release|x64.ActiveCfg = Release|x64
		{800676E5-ACA7-3EA5-ADC5-2B54FD731086}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {554CC217-51D2-3AA4-BEE5-98A1B68A961B}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
