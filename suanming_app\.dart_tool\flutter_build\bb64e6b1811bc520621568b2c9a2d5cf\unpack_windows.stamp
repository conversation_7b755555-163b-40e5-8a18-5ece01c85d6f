{"inputs": ["D:\\flutter\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\windows.dart", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.exp", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.lib", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.pdb", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_export.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_messenger.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_plugin_registrar.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_texture_registrar.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\icudtl.dat", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\binary_messenger_impl.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\byte_buffer_streams.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\core_implementations.cc", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\engine_method_result.cc", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\flutter_engine.cc", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\flutter_view_controller.cc", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\byte_streams.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\dart_project.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\encodable_value.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_channel.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_sink.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_view.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\message_codec.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_call.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_channel.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_codec.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_result.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\plugin_registrar.cc", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\readme", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\standard_codec.cc", "D:\\flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\texture_registrar_impl.h"], "outputs": ["F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\flutter_windows.dll", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\flutter_windows.dll.exp", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\flutter_windows.dll.lib", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\flutter_windows.dll.pdb", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\flutter_export.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\flutter_messenger.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\flutter_plugin_registrar.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\flutter_texture_registrar.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\flutter_windows.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\icudtl.dat", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\binary_messenger_impl.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\byte_buffer_streams.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\core_implementations.cc", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\engine_method_result.cc", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_engine.cc", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_view_controller.cc", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\byte_streams.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\dart_project.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\encodable_value.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_channel.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_sink.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\message_codec.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_call.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_channel.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_codec.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\plugin_registrar.cc", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\readme", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\standard_codec.cc", "F:\\suanming\\0730-chatui\\suanming_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\texture_registrar_impl.h"]}